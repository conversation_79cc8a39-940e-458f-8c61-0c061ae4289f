"use client";

import React from "react";
import { UserProfile as UserProfileType } from "@/types/user";
import { Noto_Sans, Space_Grotesk } from "next/font/google";

// Import broken-ink components
import HeroSection from "@/components/broken-ink/HeroSection";
import SocialMediaSection from "@/components/broken-ink/SocialMediaSection";
import ServicesSection from "@/components/broken-ink/ServicesSection";
import GallerySection from "@/components/broken-ink/GallerySection";
import ArtistsSection from "@/components/broken-ink/ArtistsSection";
import FeaturesSection from "@/components/broken-ink/FeaturesSection";
import GenericSection from "@/components/broken-ink/GenericSection";

import ContactSection from "@/components/broken-ink/ContactSection";
import ReviewsSection from "@/components/broken-ink/ReviewsSection";

const notoSans = Noto_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "700", "900"],
  variable: "--font-noto-sans",
  display: "swap",
});

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  variable: "--font-space-grotesk",
  display: "swap",
});

interface BrokenInkProfileClientProps {
  initialProfile: UserProfileType;
}

export function BrokenInkProfileClient({
  initialProfile,
}: BrokenInkProfileClientProps) {
  return (
    <div
      className={`relative flex size-full min-h-screen flex-col bg-black dark justify-between group/design-root overflow-x-hidden ${notoSans.variable} ${spaceGrotesk.variable} font-spaceGrotesk`}
    >
      <div>
        <main>
          <HeroSection profile={initialProfile} />
          <SocialMediaSection profile={initialProfile} />
          <ServicesSection profile={initialProfile} />
          <GallerySection profile={initialProfile} />
          <ArtistsSection profile={initialProfile} />
          <FeaturesSection profile={initialProfile} />
          <GenericSection profile={initialProfile} />
          <ReviewsSection profile={initialProfile} />
          <ContactSection profile={initialProfile} />
        </main>
      </div>
    </div>
  );
}
