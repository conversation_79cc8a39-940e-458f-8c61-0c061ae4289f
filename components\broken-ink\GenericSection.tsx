"use client";

import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight, Phone } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { UserProfile } from "@/types/user";
import { getButtonConfig } from "@/lib/buttonUtils";

interface GenericSectionProps {
  profile: UserProfile;
}

const GenericSection = ({ profile }: GenericSectionProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "center",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: false,
    skipSnaps: false,
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    const timer = setTimeout(() => setIsLoading(false), 100);
    return () => clearTimeout(timer);
  }, [emblaApi, onSelect]);

  if (!profile.genericSection?.enabled) {
    return null;
  }

  const sectionData = profile.genericSection;
  const buttonConfig = getButtonConfig("generic", sectionData.buttonConfig);

  const handleButtonClick = (url: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (url && url !== "#" && url.startsWith("http")) {
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  const renderCard = (item: any) => {
    const hasPrimaryButton =
      item.primaryButton.url && item.primaryButton.url !== "#";
    const hasSecondaryButton =
      item.secondaryButton.url && item.secondaryButton.url !== "#";

    return (
      <div
        key={item.id}
        className="relative overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform-gpu group rounded-xl h-96 sm:h-80 lg:h-96"
      >
        <Image
          src={item.image}
          alt={item.title}
          fill
          className="object-cover transition-transform duration-700 group-hover:scale-110"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={false}
        />
        {/* Enhanced gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"></div>

        {/* Badge for generic content */}
        {buttonConfig.showBadge && (
          <div className="absolute top-6 right-6">
            <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center transform transition-all duration-300 group-hover:scale-110 shadow-lg">
              <span className="text-white text-base font-bold">{item.id}</span>
            </div>
          </div>
        )}

        {/* Content positioned at bottom */}
        <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8">
          <h3 className="text-white font-bold text-xl sm:text-2xl mb-2 drop-shadow-lg">
            {item.title}
          </h3>
          <p className="text-white/90 text-sm sm:text-base mb-4 leading-relaxed drop-shadow-sm line-clamp-3">
            {item.description}
          </p>

          <div className="flex gap-3">
            {hasPrimaryButton && (
              <Button
                variant="default"
                size="lg"
                className="flex-1 h-14 bg-white/20 hover:bg-white/30 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl border-0 rounded-2xl text-white"
                onClick={(e) => handleButtonClick(item.primaryButton.url, e)}
              >
                {buttonConfig.primaryButtonText}
              </Button>
            )}
            {hasSecondaryButton && (
              <Button
                variant="outline"
                size="lg"
                className={`${
                  hasPrimaryButton ? "w-14 h-14 p-0" : "flex-1"
                } bg-white/10 border-white/30 hover:bg-white/20 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg rounded-2xl text-white`}
                onClick={(e) => handleButtonClick(item.secondaryButton.url, e)}
              >
                {item.secondaryButton.icon ? (
                  <i
                    className={`${item.secondaryButton.icon} ${
                      hasPrimaryButton ? "text-lg" : "mr-2"
                    } group-hover:scale-110 transition-transform duration-300`}
                    aria-hidden="true"
                  />
                ) : (
                  <Phone
                    className={`${
                      hasPrimaryButton ? "w-5 h-5" : "w-4 h-4 mr-2"
                    }`}
                  />
                )}
                {!hasPrimaryButton && buttonConfig.secondaryButtonText}
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <section className="py-16 sm:py-24 bg-black" id="generic">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {sectionData.title}
          </h2>
          {sectionData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {sectionData.description}
            </p>
          )}
        </div>

        {/* Carousel */}
        <div className="relative">
          <div
            className={`overflow-hidden rounded-2xl transition-opacity duration-300${
              isLoading ? "opacity-0" : "opacity-100"
            }`}
            ref={emblaRef}
          >
            <div className="flex gap-4 lg:gap-6">
              {sectionData.items.map((item, index) => (
                <div
                  key={item.id}
                  className="flex-[0_0_85%] sm:flex-[0_0_75%] md:flex-[0_0_65%] lg:flex-[0_0_360px] min-w-0"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {renderCard(item)}
                </div>
              ))}
            </div>
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-center mt-8 gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={scrollPrev}
              disabled={!canScrollPrev}
              className="w-12 h-12 rounded-full p-0 bg-white/20 border-white/30 hover:bg-white/30 backdrop-blur-sm transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform text-white"
              aria-label="Conteúdo anterior"
            >
              <ChevronLeft className="w-5 h-5" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={scrollNext}
              disabled={!canScrollNext}
              className="w-12 h-12 rounded-full p-0 bg-white/20 border-white/30 hover:bg-white/30 backdrop-blur-sm transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform text-white"
              aria-label="Próximo conteúdo"
            >
              <ChevronRight className="w-5 h-5" />
            </Button>
          </div>

          {/* Progress indicator */}
          <div className="flex justify-center mt-4 gap-2">
            {sectionData.items.map((_, index) => (
              <div
                key={index}
                className="w-2 h-2 rounded-full transition-all duration-300 cursor-pointer bg-white"
                style={{
                  opacity: selectedIndex === index ? 1 : 0.4,
                  transform:
                    selectedIndex === index ? "scale(1.25)" : "scale(1)",
                }}
                onMouseEnter={(e) => {
                  if (selectedIndex !== index) {
                    e.currentTarget.style.opacity = "0.6";
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedIndex !== index) {
                    e.currentTarget.style.opacity = "0.4";
                  }
                }}
                onClick={() => emblaApi?.scrollTo(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default GenericSection;
